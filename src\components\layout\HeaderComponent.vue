<template>
    <div class="header-container">
        <!-- 中间区域 -->
        <div class="header-center">
        </div>

        <!-- 右侧时间和用户信息 -->
        <div class="header-right">
            <!-- 时间日期区域 -->
            <div class="time-section">
                <div class="time-display">{{ currentTime }}</div>
                <div class="date-display">{{ currentDate }}</div>
            </div>

            <!-- 天气信息区域 -->
            <div class="weather-section">
                <div class="weather-display">多云</div>
                <div class="weather-details">
                    <span class="temperature">14°C</span>
                    <span class="wind">微风</span>
                </div>
            </div>

            <!-- 用户头像 -->
            <div class="user-avatar">
                <span class="weather-icon">☁️</span>
            </div>

            <!-- 切换项目icon -->
            <div class="project-switch" @click="toggleProjectDropdown" ref="projectDropdownRef">
                <svg-icon icon-class="切换项目" class-name="project-icon" />
                <!-- 项目下拉菜单 -->
                <div v-if="showProjectDropdown" class="project-dropdown">
                  
                    <div
                        v-for="project in projectList"
                        :key="project.id"
                        class="dropdown-item"
                        :class="{ active: selectedProject === project.id }"
                        @click.stop="selectProject(project.id)"
                    >
                        {{ project.name }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// 定义emit事件
const emit = defineEmits(['project-change'])

// 当前时间和日期
const currentTime = ref('')
const currentDate = ref('')



// 当前选中的项目
const selectedProject = ref('nanjing-bank')

// 项目下拉框显示状态
const showProjectDropdown = ref(false)
const projectDropdownRef = ref(null)

// 项目列表数据
const projectList = ref([
    {
        id: 'nanjing-bank',
        name: '南京银行项目'
    },
    {
        id: 'hongwu-bank',
        name: '洪武银行项目'
    }
])

// 更新时间的函数
const updateTime = () => {
    const now = new Date()

    // 格式化时间 (HH:MM:SS)
    currentTime.value = now.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    })

    // 格式化日期 (星期X YYYY-MM-DD)
    const weekday = now.toLocaleDateString('zh-CN', { weekday: 'long' })
    const dateStr = now.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    }).replace(/\//g, '-')
    
    currentDate.value = `${weekday} ${dateStr}`
}



// 切换项目下拉框显示状态
const toggleProjectDropdown = () => {
    showProjectDropdown.value = !showProjectDropdown.value
}

// 选择项目
const selectProject = (projectId) => {
    selectedProject.value = projectId
    showProjectDropdown.value = false
    const selectedProjectData = projectList.value.find(p => p.id === projectId)
    console.log('HeaderComponent - 切换项目到:', selectedProjectData?.name)
    console.log('HeaderComponent - 发送project-change事件:', projectId)
    emit('project-change', projectId)
}

// 点击外部关闭下拉框
const handleClickOutside = (event) => {
    if (projectDropdownRef.value && !projectDropdownRef.value.contains(event.target)) {
        showProjectDropdown.value = false
    }
}



// 定时器
let timeInterval = null

onMounted(() => {
    updateTime()
    // 每秒更新一次时间
    timeInterval = setInterval(updateTime, 1000)
    // 添加全局点击事件监听
    document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
    if (timeInterval) {
        clearInterval(timeInterval)
    }
    // 移除全局点击事件监听
    document.removeEventListener('click', handleClickOutside)
})
</script>

<style lang="less" scoped>
.header-container {
    width: 120%;
    height: 81px;
    background-image: url('@/assets/images/<EMAIL>');
    background-size: 100% 81px;
    background-repeat: no-repeat;
    background-position: center;

    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 400px 0 24px;
    position: relative;
    z-index: 1000;
}

.header-center {
    display: flex;
    justify-content: flex-start;
    margin-left: 530px;
    flex: 1;
    height: 81px;
    align-items: flex-start;
    position: relative;


}

.header-right {
    display: flex;
    align-items: center;
    gap: 25px;
  
    .time-section {
        display: flex;
        flex-direction: column;
        align-items: flex-end;

        gap: 10px;
        height: 100%;

        .time-display {
            font-family: D-DIN;
            font-weight: 700;
            font-size: 24px;
            color: #fff;
            line-height: 26px;
        }

        .date-display {
            font-family: Source Han Sans;
            font-weight: 400;
            font-size: 14px;
            color: #fff;
            line-height: 20px;
        }
    }



    .weather-section {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
    
        gap: 10px;
        height: 100%;

        .weather-display {
            font-family: Source Han Sans;
            font-weight: 700;
            font-size: 24px;
            color: #fff;
            line-height: 26px;
        }

        .weather-details {
            font-family: Source Han Sans;
            font-weight: 400;
            font-size: 14px;
            color: #fff;
            line-height: 20px;
            opacity: .7;
            display: flex;
            gap: 12px;
            align-items: center;

            .temperature {
                white-space: nowrap;
            }

            .wind {
                white-space: nowrap;
            }
        }
    }

    .user-avatar {
        height: 56px;
        width: 56px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        .weather-icon {
            height: 56px;
            width: 56px;
            font-size: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    .project-switch {
        height: 56px;
        width: 56px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        position: relative;
        margin-left: -33px;
        .project-icon {
            width: 24px;
            height: 24px;
            color: #fff;
        }

        // 项目下拉框样式
        .project-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            margin-top: 8px;
            background: rgba(0, 20, 40, 0.95);
            border: 1px solid rgba(0, 103, 202, 0.5);
            border-radius: 6px;
            min-width: 160px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            backdrop-filter: blur(10px);

            .dropdown-header {
                padding: 12px 16px 8px;
                font-family: 'Source Han Sans';
                font-size: 12px;
                font-weight: 400;
                color: rgba(255, 255, 255, 0.6);
                border-bottom: 1px solid rgba(0, 103, 202, 0.3);
                text-align: center;
            }

            .dropdown-item {
                padding: 10px 16px;
                font-family: 'Source Han Sans';
                font-size: 14px;
                font-weight: 400;
                color: #fff;
                cursor: pointer;
                transition: all 0.3s ease;
                white-space: nowrap;

                &:hover {
                    background: rgba(0, 103, 202, 0.3);
                }

                &.active {
                    background: rgba(0, 103, 202, 0.5);
                    color: #00d4ff;
                    position: relative;

                    &::before {
                        content: '✓';
                        position: absolute;
                        right: 12px;
                        top: 50%;
                        transform: translateY(-50%);
                        color: #00d4ff;
                        font-weight: bold;
                    }
                }

                &:last-child {
                    border-radius: 0 0 6px 6px;
                }
            }
        }
    }
}


</style>